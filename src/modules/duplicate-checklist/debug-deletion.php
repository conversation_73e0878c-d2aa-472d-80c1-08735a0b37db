<?php
/**
 * Debug script to test duplicate deletion
 * 
 * This script helps debug why duplicated checklists are not being properly deleted from the database.
 * Run this from WordPress admin or via WP-CLI to test the deletion process.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Debug duplicate deletion process
 */
function debug_duplicate_deletion() {
    echo "<h2>Duplicate Deletion Debug</h2>";
    
    // Get current options
    $options = get_option('publishpress_checklists_checklists_options', new stdClass());
    
    if (!is_object($options)) {
        echo "<p style='color: red;'>❌ Options not loaded as object</p>";
        return;
    }
    
    echo "<p>✅ Options loaded successfully</p>";
    
    // Find all duplicate entries
    $all_keys = array_keys((array) $options);
    $duplicate_keys = array_filter($all_keys, function($key) {
        return strpos($key, '_duplicate_') !== false;
    });
    
    echo "<h3>Found Duplicate Keys (" . count($duplicate_keys) . "):</h3>";
    if (empty($duplicate_keys)) {
        echo "<p>No duplicate keys found in database.</p>";
        return;
    }
    
    echo "<ul>";
    foreach ($duplicate_keys as $key) {
        echo "<li><code>$key</code></li>";
    }
    echo "</ul>";
    
    // Group by duplicate name
    $duplicate_groups = [];
    foreach ($duplicate_keys as $key) {
        // Extract the base duplicate name
        if (preg_match('/^(.+_duplicate_\d+)/', $key, $matches)) {
            $base_name = $matches[1];
            if (!isset($duplicate_groups[$base_name])) {
                $duplicate_groups[$base_name] = [];
            }
            $duplicate_groups[$base_name][] = $key;
        }
    }
    
    echo "<h3>Grouped by Duplicate Name:</h3>";
    foreach ($duplicate_groups as $base_name => $keys) {
        echo "<h4>$base_name (" . count($keys) . " keys)</h4>";
        echo "<ul>";
        foreach ($keys as $key) {
            $value = $options->{$key};
            $value_preview = is_array($value) || is_object($value) ? 
                json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) : 
                $value;
            echo "<li><code>$key</code>: " . htmlspecialchars(substr($value_preview, 0, 100)) . "...</li>";
        }
        echo "</ul>";
    }
    
    // Test deletion for one duplicate (if any exist)
    if (!empty($duplicate_groups)) {
        $test_duplicate = array_keys($duplicate_groups)[0];
        echo "<h3>Testing Deletion for: $test_duplicate</h3>";
        
        // Load the duplicate handler
        require_once __DIR__ . '/lib/DuplicateHandler.php';
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();
        
        echo "<p><strong>Before deletion:</strong></p>";
        $before_keys = array_filter($all_keys, function($key) use ($test_duplicate) {
            return strpos($key, $test_duplicate) === 0;
        });
        echo "<ul>";
        foreach ($before_keys as $key) {
            echo "<li><code>$key</code></li>";
        }
        echo "</ul>";
        
        // Perform deletion (dry run first)
        echo "<p><em>Note: This is a dry run. To actually delete, uncomment the deletion code below.</em></p>";
        
        /*
        // Uncomment this block to actually perform deletion
        $result = $handler->deleteDuplicateRequirement($test_duplicate);

        if ($result['success']) {
            echo "<p style='color: green;'>✅ Deletion successful: " . $result['message'] . "</p>";

            // Check what remains
            $options_after = get_option('publishpress_checklists_checklists_options', new stdClass());
            $all_keys_after = array_keys((array) $options_after);
            $remaining_keys = array_filter($all_keys_after, function($key) use ($test_duplicate) {
                return strpos($key, $test_duplicate) === 0;
            });

            echo "<p><strong>After deletion:</strong></p>";
            if (empty($remaining_keys)) {
                echo "<p style='color: green;'>✅ All keys successfully removed</p>";
            } else {
                echo "<p style='color: red;'>❌ Some keys still remain:</p>";
                echo "<ul>";
                foreach ($remaining_keys as $key) {
                    echo "<li><code>$key</code></li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ Deletion failed: " . $result['message'] . "</p>";
        }
        */

        // Add cleanup button
        echo "<h3>Comprehensive Cleanup</h3>";
        echo "<p>If individual deletion is not working, you can run a comprehensive cleanup that removes ALL duplicate data:</p>";
        echo "<button type='button' onclick='runCleanup()' class='button button-secondary'>🧹 Clean Up All Orphaned Duplicates</button>";
        echo "<div id='cleanup-result'></div>";

        // Add JavaScript for cleanup
        echo "<script>
        function runCleanup() {
            if (!confirm('This will remove ALL duplicate checklist data from the database. Are you sure?')) {
                return;
            }

            document.getElementById('cleanup-result').innerHTML = '<p>Running cleanup...</p>';

            fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'ppc_cleanup_orphaned_duplicates',
                    _wpnonce: '" . wp_create_nonce('ppc_duplicate_checklist') . "'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('cleanup-result').innerHTML =
                        '<div style=\"background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0;\">' +
                        '<strong>✅ Cleanup successful!</strong><br>' +
                        'Options removed: ' + data.data.options_removed + '<br>' +
                        'Mappings removed: ' + data.data.mappings_removed + '<br>' +
                        'Duplicates found: ' + data.data.duplicates_found.join(', ') +
                        '</div>';
                } else {
                    document.getElementById('cleanup-result').innerHTML =
                        '<div style=\"background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0;\">' +
                        '<strong>❌ Cleanup failed:</strong> ' + data.data +
                        '</div>';
                }
            })
            .catch(error => {
                document.getElementById('cleanup-result').innerHTML =
                    '<div style=\"background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0;\">' +
                    '<strong>❌ Error:</strong> ' + error.message +
                    '</div>';
            });
        }
        </script>";
    }
}

// Check if we're in WordPress admin and user has proper permissions
if (is_admin() && current_user_can('manage_options')) {
    // Add admin page or run directly
    if (isset($_GET['debug_duplicate_deletion'])) {
        debug_duplicate_deletion();
    } else {
        echo "<p><a href='" . admin_url('admin.php?page=ppch-checklists&debug_duplicate_deletion=1') . "'>Run Duplicate Deletion Debug</a></p>";
    }
}
