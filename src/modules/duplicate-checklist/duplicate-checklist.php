<?php

/**
 * @package     PublishPress\ChecklistsPro
 * <AUTHOR> <<EMAIL>>
 * @copyright   copyright (C) 2019 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

// Prevent direct access
defined('ABSPATH') or die('No direct script access allowed.');

use PublishPress\Checklists\Core\Legacy\LegacyPlugin;
use PublishPress\Checklists\Core\Legacy\Module;
use PublishPress\ChecklistsPro\Factory;
use PublishPress\ChecklistsPro\HooksAbstract as PPCHPROHooksAbstract;
use WPPF\Plugin\ServicesAbstract;
use WPPF\WP\HooksHandlerInterface;

/**
 * Class PPCH_Duplicate_Checklist
 * 
 * Handles duplication of checklist requirements with role-based configurations
 */
#[\AllowDynamicProperties]
class PPCH_Duplicate_Checklist extends Module
{
    const SETTINGS_SLUG = 'pp-duplicate-checklist-prosettings';
    
    const DUPLICATE_META_KEY = 'pp_duplicate_checklist_meta';
    
    public $module_name = 'duplicate_checklist';

    /**
     * Instance for the module
     *
     * @var stdClass
     */
    public $module;

    /**
     * @var LegacyPlugin
     */
    private $legacyPlugin;

    /**
     * @var HooksHandlerInterface
     */
    private $hooksHandler;

    /**
     * @var string
     */
    private $pluginFile;

    /**
     * @var string
     */
    private $pluginVersion;

    /**
     * Construct the PPCH_Duplicate_Checklist class
     */
    public function __construct()
    {
        // Prevent multiple instantiations
        static $instance_created = false;
        if ($instance_created) {
            return;
        }
        $instance_created = true;

        // Check if required classes exist
        if (!class_exists('PublishPress\\ChecklistsPro\\Factory')) {
            return;
        }

        try {
            $container = Factory::getContainer();

            $this->legacyPlugin  = $container->get(ServicesAbstract::LEGACY_PLUGIN);
            $this->hooksHandler  = $container->get(ServicesAbstract::HOOKS_HANDLER);
            $this->pluginFile    = $container->get(ServicesAbstract::PLUGIN_FILE);
            $this->pluginVersion = $container->get(ServicesAbstract::PLUGIN_VERSION);
        } catch (\Exception $e) {
            error_log('Error initializing Duplicate Checklist module: ' . $e->getMessage());
            return;
        }

        $this->module_url = $this->getModuleUrl(__FILE__);

        // Register the module with PublishPress
        $args = [
            'title'                => esc_html__(
                'Duplicate Checklist',
                'publishpress-checklists-pro'
            ),
            'short_description'    => esc_html__(
                'Duplicate checklist requirements with different configurations',
                'publishpress-checklists-pro'
            ),
            'extended_description' => esc_html__(
                'Create duplicate checklist requirements with different configurations for role-based requirements',
                'publishpress-checklists-pro'
            ),
            'module_url'           => $this->module_url,
            'icon_class'           => 'dashicons dashicons-admin-page',
            'slug'                 => 'duplicate-checklist',
            'default_options'      => [
                'enabled' => 'on',
            ],
            'options_page'         => false,
            'autoload'             => true,
        ];

        // Apply a filter to the default options
        $args['default_options'] = $this->hooksHandler->applyFilters(
            PPCHPROHooksAbstract::FILTER_WOOCOMMERCE_DEFAULT_OPTIONS,
            $args['default_options']
        );

        try {
            $this->module = $this->legacyPlugin->register_module($this->module_name, $args);

            // Ensure the module instance is properly set in the legacy plugin
            if ($this->module && $this->legacyPlugin) {
                $this->legacyPlugin->{$this->module_name} = $this;
            }

            $this->hooksHandler->addAction(PPCHPROHooksAbstract::ACTION_CHECKLIST_LOAD_ADDONS, [$this, 'actionLoadAddons']);

            // Register hooks immediately like status-filter module
            $this->registerHooks();

            
        } catch (\Exception $e) {
            error_log('Error registering Duplicate Checklist module: ' . $e->getMessage());
            return;
        }
    }

    /**
     * Register hooks immediately (like status-filter module)
     */
    private function registerHooks()
    {
        try {
            // Add hooks for duplicate functionality (priority 5 to appear first)
            add_action('publishpress_checklists_tasks_list_th', [$this, 'addDuplicateColumnHeader'], 30);
            add_action('publishpress_checklists_tasks_list_td', [$this, 'addDuplicateColumnCell'], 30, 2);

            // AJAX handlers
            add_action('wp_ajax_ppc_duplicate_requirement', [$this, 'ajaxDuplicateRequirement']);
            add_action('wp_ajax_ppc_delete_duplicate_requirement', [$this, 'ajaxDeleteDuplicateRequirement']);
            add_action('wp_ajax_ppc_rename_duplicate_requirement', [$this, 'ajaxRenameDuplicateRequirement']);
            add_action('wp_ajax_ppc_cleanup_orphaned_duplicates', [$this, 'ajaxCleanupOrphanedDuplicates']);


            // Enqueue assets
            add_action('admin_enqueue_scripts', [$this, 'enqueueAssets']);

            // Add debug functionality if requested
            add_action('admin_notices', [$this, 'addDebugNotice']);
            add_action('admin_init', [$this, 'handleDebugRequest']);

            // Load test functionality in development
            if (defined('WP_DEBUG') && WP_DEBUG && file_exists(__DIR__ . '/test-duplicate.php')) {
                require_once __DIR__ . '/test-duplicate.php';
            }

            
        } catch (\Exception $e) {
            error_log('Error registering Duplicate Checklist module hooks: ' . $e->getMessage());
        }
    }

    /**
     * Initialize the module. Conditionally loads if the module is enabled
     */
    public function init()
    {
        // This method is kept for compatibility but hooks are registered in constructor
    }

    /**
     * Action triggered before load requirements. We use this
     * to load the filters.
     */
    public function actionLoadAddons()
    {
        $this->hooksHandler->addFilter(
            PPCHPROHooksAbstract::FILTER_POST_TYPE_REQUIREMENTS,
            [$this, 'filterPostTypeRequirements'],
            10,
            2
        );
        $this->hooksHandler->addAction(
            PPCHPROHooksAbstract::ACTION_CHECKLIST_ENQUEUE_SCRIPTS,
            [$this, 'enqueueAdminScripts']
        );
    }

    /**
     * Add duplicate column header to global checklists table
     */
    public function addDuplicateColumnHeader()
    {
        
        echo '<th title="' . esc_attr__('Duplicate Requirement', 'publishpress-checklists-pro') . '">Actions</th>';
    }

    /**
     * Add duplicate button to each requirement row
     *
     * @param object $requirement The requirement object
     * @param string $post_type The post type
     */
    public function addDuplicateColumnCell($requirement, $post_type)
    {
        

        // Check if this is a duplicated requirement
        $is_duplicate = strpos($requirement->name, '_duplicate_') !== false;

        // Skip custom items
        if (strpos($requirement->name, 'custom_') === 0) {
            echo '<td></td>';
            return;
        }

        if ($is_duplicate) {
            // Show management buttons for duplicated requirements
            $buttons = sprintf(
                '<td class="ppc-duplicate-actions">
                    <button type="button" class="button button-secondary ppc-delete-duplicate-btn" data-requirement="%s" data-post-type="%s" title="%s">
                        ' . esc_html__('Delete', 'publishpress-checklists-pro') . '
                    </button>
                </td>',
                esc_attr($requirement->name),
                esc_attr($post_type),
                esc_attr__('Delete this duplicate', 'publishpress-checklists-pro')
            );
        } else {
            // Check if the requirement is disabled for this post type
            $is_disabled = $this->isRequirementDisabled($requirement->name, $post_type);

            if ($is_disabled) {
                // Show disabled duplicate button
                $buttons = sprintf(
                    '<td><button type="button" class="button button-secondary ppc-duplicate-btn" data-requirement="%s" data-post-type="%s" title="%s" disabled>
                        ' . esc_html__('Duplicate', 'publishpress-checklists-pro') . '
                    </button></td>',
                    esc_attr($requirement->name),
                    esc_attr($post_type),
                    esc_attr__('Cannot duplicate disabled requirement', 'publishpress-checklists-pro')
                );
            } else {
                // Show normal duplicate button for enabled requirements
                $buttons = sprintf(
                    '<td><button type="button" class="button button-secondary ppc-duplicate-btn" data-requirement="%s" data-post-type="%s" title="%s">
                        ' . esc_html__('Duplicate', 'publishpress-checklists-pro') . '
                    </button></td>',
                    esc_attr($requirement->name),
                    esc_attr($post_type),
                    esc_attr__('Duplicate this requirement', 'publishpress-checklists-pro')
                );
            }
        }

        echo $buttons;
    }

    /**
     * AJAX handler for duplicating requirements
     */
    public function ajaxDuplicateRequirement()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'] ?? '', 'ppc_duplicate_checklist')) {
            wp_send_json_error('Invalid nonce');
        }

        // Verify permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        $requirement_name = sanitize_text_field($_POST['requirement'] ?? '');
        $post_type = sanitize_text_field($_POST['post_type'] ?? '');
        $custom_name = sanitize_text_field($_POST['custom_name'] ?? '');

        if (empty($requirement_name) || empty($post_type)) {
            wp_send_json_error('Missing required parameters');
        }

        // Load the duplicate handler
        require_once __DIR__ . '/lib/DuplicateHandler.php';
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();

        $result = $handler->duplicateRequirement($requirement_name, $post_type, $custom_name);

        if ($result['success']) {
            // Add debug information
            $result['debug'] = $handler->debugDuplicate($result['duplicate_name']);
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for deleting duplicate requirements
     */
    public function ajaxDeleteDuplicateRequirement()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'] ?? '', 'ppc_duplicate_checklist')) {
            wp_send_json_error('Invalid nonce');
        }

        // Verify permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        $requirement_name = sanitize_text_field($_POST['requirement'] ?? '');

        if (empty($requirement_name) || strpos($requirement_name, '_duplicate_') === false) {
            wp_send_json_error('Invalid requirement name');
        }

        // Load the duplicate handler
        require_once __DIR__ . '/lib/DuplicateHandler.php';
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();

        $result = $handler->deleteDuplicateRequirement($requirement_name);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for renaming duplicate requirements
     */
    public function ajaxRenameDuplicateRequirement()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'] ?? '', 'ppc_duplicate_checklist')) {
            wp_send_json_error('Invalid nonce');
        }

        // Verify permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        $requirement_name = sanitize_text_field($_POST['requirement'] ?? '');
        $new_name = sanitize_text_field($_POST['new_name'] ?? '');

        if (empty($requirement_name) || empty($new_name) || strpos($requirement_name, '_duplicate_') === false) {
            wp_send_json_error('Invalid parameters');
        }

        // Load the duplicate handler
        require_once __DIR__ . '/lib/DuplicateHandler.php';
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();

        $result = $handler->renameDuplicateRequirement($requirement_name, $new_name);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for cleaning up orphaned duplicate requirements
     */
    public function ajaxCleanupOrphanedDuplicates()
    {
        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'] ?? '', 'ppc_duplicate_checklist')) {
            wp_send_json_error('Invalid nonce');
        }

        // Verify permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Unauthorized');
        }

        // Load the duplicate handler
        require_once __DIR__ . '/lib/DuplicateHandler.php';
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();

        $result = $handler->cleanupOrphanedDuplicates();

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Add debug notice for duplicate deletion issues
     */
    public function addDebugNotice()
    {
        // Only show on checklist pages and to admins
        if (!current_user_can('manage_options')) {
            return;
        }

        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'checklist') === false) {
            return;
        }

        // Check if there are any duplicate entries in the database
        $options = get_option('publishpress_checklists_checklists_options', new \stdClass());
        if (!is_object($options)) {
            return;
        }

        $existing_keys = array_keys((array) $options);
        $duplicate_keys = array_filter($existing_keys, function($key) {
            return strpos($key, '_duplicate_') !== false;
        });

        if (!empty($duplicate_keys)) {
            $debug_url = admin_url('admin.php?page=ppch-checklists&debug_duplicate_deletion=1');
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p><strong>Duplicate Checklist Debug:</strong> Found ' . count($duplicate_keys) . ' duplicate entries in database. ';
            echo '<a href="' . esc_url($debug_url) . '" class="button button-small">Debug & Clean Up</a></p>';
            echo '</div>';
        }
    }

    /**
     * Handle debug requests
     */
    public function handleDebugRequest()
    {
        if (isset($_GET['debug_duplicate_deletion']) && current_user_can('manage_options')) {
            // Include the debug script
            require_once __DIR__ . '/debug-deletion.php';

            // Run the debug function if it exists
            if (function_exists('debug_duplicate_deletion')) {
                add_action('admin_notices', function() {
                    echo '<div class="notice notice-info">';
                    debug_duplicate_deletion();
                    echo '</div>';
                });
            }
        }
    }

    /**
     * Enqueue assets for the duplicate functionality
     */
    public function enqueueAssets($hook)
    {
        $base_url = plugin_dir_url(__FILE__);
        $version = $this->pluginVersion;

        // Enqueue script for the admin checklist table page
        wp_enqueue_script(
            'ppc-duplicate-checklist',
            $base_url . 'assets/js/duplicate-checklist.js',
            ['jquery'],
            $version,
            true
        );

        // Enqueue CSS
        wp_enqueue_style(
            'ppc-duplicate-checklist',
            $base_url . 'assets/css/duplicate-checklist.css',
            [],
            $version
        );

        // Localize script
        wp_localize_script(
            'ppc-duplicate-checklist',
            'ppcDuplicateChecklist',
            [
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('ppc_duplicate_checklist'),
                'strings' => [
                    'confirm_duplicate' => __('Are you sure you want to duplicate this requirement?', 'publishpress-checklists-pro'),
                    'duplicating' => __('Duplicating...', 'publishpress-checklists-pro'),
                    'success' => __('Requirement duplicated successfully!', 'publishpress-checklists-pro'),
                    'error' => __('Error duplicating requirement.', 'publishpress-checklists-pro'),
                ]
            ]
        );
    }

    /**
     * Enqueue admin scripts for validation
     */
    public function enqueueAdminScripts()
    {
        // Validation is now handled directly in duplicate-checklist.js
        // No separate validation script needed
    }

    /**
     * Check if a requirement is disabled for a specific post type
     *
     * @param string $requirement_name
     * @param string $post_type
     * @return bool
     */
    private function isRequirementDisabled($requirement_name, $post_type)
    {
        // Fetch checklist options. Depending on WordPress version or previous
        // plugin releases this can come back as an array **or** stdClass.
        $options = get_option('publishpress_checklists_checklists_options', []);

        // Work with both formats seamlessly.
        $options_array = is_object($options) ? (array) $options : (array) $options;

        $rule_key = $requirement_name . '_rule';

        if (!isset($options_array[$rule_key])) {
            return false;
        }

        $rule_value = $options_array[$rule_key];

        // Extract the status for the current post-type regardless of the data structure.
        if (is_object($rule_value)) {
            $status = $rule_value->{$post_type} ?? null;
        } elseif (is_array($rule_value)) {
            $status = $rule_value[$post_type] ?? null;
        } else {
            // Scalar fallback (legacy data where the rule itself is the value)
            $status = $rule_value;
        }

        // Treat the following values as disabled
        return in_array($status, ['off', 'disabled', 0, '0', false, 'false'], true);
    }

    /**
     * Get the original requirement type from duplicate name
     *
     * @param string $duplicate_name
     * @return string|false
     */
    private function getOriginalType($duplicate_name)
    {
        // Extract original type from duplicate name
        // Format: {original_name}_duplicate_{number}
        $parts = explode('_duplicate_', $duplicate_name);
        return isset($parts[0]) ? $parts[0] : false;
    }

    /**
     * Filter post type requirements to include duplicated requirements
     *
     * @param array $requirements
     * @param string $postType
     * @return array
     */
    public function filterPostTypeRequirements($requirements, $postType)
    {
        // Load the duplicate handler
        require_once __DIR__ . '/lib/DuplicateHandler.php';
        $handler = new \PublishPress\ChecklistsPro\DuplicateChecklist\DuplicateHandler();

        // Get duplicated requirements for this post type
        $duplicatedRequirements = $handler->getDuplicatedRequirements($postType);

        // Add duplicated requirements to the list
        foreach ($duplicatedRequirements as $duplicateClass) {
            $requirements[] = $duplicateClass;
        }

        return $requirements;
    }
}
